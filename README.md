## Personal Library Management System

This is a simple document management system as a student-level project. So, it is not production-ready and not intended to be used in a production environment.

Tech stack:
1. Node.js
2. Express.js
3. Handlebars
4. Passport.js (email and password)
5. SQLite
6. Bulma CSS
7. Sequelize
8. Multer

Requirements:
Minimal but elegant UI design.
Code neither too complicated nor too lengthy.
Try to keep most logic in controller, route, and model files.
Create any necessary middleware.
Create policies for authorizing the controller methods.
Components should be re-used using partials where possible, e.g. header, footer, forms, layout, tables, etc.

Concepts:
No registration.
Users are managed by admin-level users.
Each user has a quota of documents by size (in MB) they can upload total, which can be set by admin-level users and defaults to 100 MB.
So, only two models: User and Document.
Users can only view and edit their own documents.

Features:
1. User login page with email and password. No registration required.
2. Dashboard to list all of their uploaded documents in a table.
3. Option to upload a document. In upload page, user can select a file and upload it. User is allowed to upload only if their quota is not exceeded. It also shows the used/remaining quota.
4. Option to delete a document. User can delete a document only if they are the owner of the document.
5. Option to logout.
6. Clicking on a document opens a modal to view the document. It shows the name, authors, and size of the document along with upload date. It also has a link to download the document. It also has a link to open the document in a new tab. It also has an option to edit the name and authors field of the document.
7. Admin-level users can access the admin dashboard from the top-right corner. It goes into the /admin route. They can access a list of all users from this page. Clicking on a user opens a modal to view the user. It shows the email, quota, and documents uploaded by the user. It also has an option to edit the quota of the user. They can also add a new user from this page (email, quota, and password).
8. Admin-level users can also access a list of all documents from this page. Clicking on a document opens a modal to view the document. It shows the name, authors, and size of the document along with upload date. It also has an option to edit the name and authors field of the document.


Other Details:
1. Admin-level users are created by default with email "<EMAIL>" and password "admin".
2. First normal user is created by default with email "<EMAIL>" and password "user".