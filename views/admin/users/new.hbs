{{!< ../../layout}}

<div class="columns is-centered">
  <div class="column is-8">
    <div class="box">
      <h1 class="title">Add New User</h1>
      
      {{#if error_msg}}
        <div class="notification is-danger">
          <button class="delete"></button>
          {{error_msg}}
        </div>
      {{/if}}
      
      {{#if errors}}
        <div class="notification is-danger">
          <button class="delete"></button>
          <ul>
            {{#each errors}}
              <li>{{this.msg}}</li>
            {{/each}}
          </ul>
        </div>
      {{/if}}
      
      <form method="POST" action="/admin/users">
        <div class="field">
          <label class="label">Email</label>
          <div class="control has-icons-left">
            <input 
              class="input {{#if errors.email}}is-danger{{/if}}" 
              type="email" 
              name="email" 
              placeholder="Email address"
              value="{{user.email}}"
              required
            >
            <span class="icon is-small is-left">
              <i class="fas fa-envelope"></i>
            </span>
          </div>
          {{#if errors.email}}
            <p class="help is-danger">{{errors.email.msg}}</p>
          {{/if}}
        </div>
        
        <div class="field">
          <label class="label">Password</label>
          <div class="control has-icons-left">
            <input 
              class="input {{#if errors.password}}is-danger{{/if}}" 
              type="password" 
              name="password" 
              placeholder="Password"
              required
              minlength="6"
            >
            <span class="icon is-small is-left">
              <i class="fas fa-lock"></i>
            </span>
          </div>
          {{#if errors.password}}
            <p class="help is-danger">{{errors.password.msg}}</p>
          {{/if}}
        </div>
        
        <div class="field">
          <label class="label">Storage Quota (MB)</label>
          <div class="control">
            <input 
              class="input {{#if errors.quota_mb}}is-danger{{/if}}" 
              type="number" 
              name="quota_mb" 
              min="10" 
              step="1"
              value="{{user.quota_mb}}"
              required
            >
          </div>
          <p class="help">Maximum storage space in MB. Default is 100 MB.</p>
          {{#if errors.quota_mb}}
            <p class="help is-danger">{{errors.quota_mb.msg}}</p>
          {{/if}}
        </div>
        
        <div class="field">
          <div class="control">
            <label class="checkbox">
              <input type="checkbox" name="is_admin" {{#if user.is_admin}}checked{{/if}}>
              Administrator privileges
            </label>
          </div>
        </div>
        
        <div class="field is-grouped">
          <div class="control">
            <button type="submit" class="button is-primary">
              <span class="icon">
                <i class="fas fa-save"></i>
              </span>
              <span>Create User</span>
            </button>
          </div>
          <div class="control">
            <a href="/admin/users" class="button is-light">
              <span class="icon">
                <i class="fas fa-times"></i>
              </span>
              <span>Cancel</span>
            </a>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

{{#content "scripts"}}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Close notification when clicking the delete button
    document.querySelectorAll('.notification .delete').forEach(deleteButton => {
      deleteButton.addEventListener('click', function() {
        this.closest('.notification').remove();
      });
    });
  });
</script>
{{/content}}
