{{!< ../layout}}

<div class="level">
  <div class="level-left">
    <h1 class="title">Manage Users</h1>
  </div>
  <div class="level-right">
    <div class="buttons">
      <a href="/admin/users/new" class="button is-primary">
        <span class="icon">
          <i class="fas fa-user-plus"></i>
        </span>
        <span>Add User</span>
      </a>
    </div>
  </div>
</div>

{{#if success_msg}}
  <div class="notification is-success">
    <button class="delete"></button>
    {{success_msg}}
  </div>
{{/if}}

{{#if error_msg}}
  <div class="notification is-danger">
    <button class="delete"></button>
    {{error_msg}}
  </div>
{{/if}}

<div class="box">
  <div class="table-container">
    <table class="table is-fullwidth is-hoverable">
      <thead>
        <tr>
          <th>Name</th>
          <th>Email</th>
          <th>Role</th>
          <th>Storage Used</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        {{#each users}}
          {{> user-table-row this}}
        {{else}}
          <tr>
            <td colspan="5" class="has-text-centered">
              <div class="has-text-centered py-5">
                <span class="icon is-large has-text-grey-light">
                  <i class="fas fa-users fa-3x"></i>
                </span>
                <p class="has-text-grey">No users found</p>
              </div>
            </td>
          </tr>
        {{/each}}
      </tbody>
    </table>
  </div>
</div>

{{> delete-user-modal}}

{{> user-modals-script}}