{{!< ../layout}}

<div class="columns">
  <div class="column is-8 is-offset-2">
    <div class="box">
      <div class="level">
        <div class="level-left">
          <h1 class="title">
            <span class="icon-text">
              <span class="icon">
                <i class="fas {{#if (eq document.mime_type 'application/pdf')}}fa-file-pdf has-text-danger{{else if (or (eq document.mime_type 'application/msword') (eq document.mime_type 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'))}}fa-file-word has-text-info{{else if (or (eq document.mime_type 'application/vnd.ms-excel') (eq document.mime_type 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'))}}fa-file-excel has-text-success{{else if (eq document.mime_type 'text/plain')}}fa-file-alt{{else if (eq (split document.mime_type '/')[0] 'image')}}fa-file-image{{else}}fa-file{{/if}} fa-2x"></i>
              </span>
              <span>{{document.name}}</span>
            </span>
          </h1>
        </div>
        <div class="level-right">
          <div class="buttons">
            <a href="/documents/{{document.id}}/download" class="button is-primary">
              <span class="icon">
                <i class="fas fa-download"></i>
              </span>
              <span>Download</span>
            </a>
            {{#if (or (eq user.role 'admin') (eq user.id document.UserId))}}
              <button class="button is-danger delete-document" data-id="{{document.id}}">
                <span class="icon">
                  <i class="fas fa-trash"></i>
                </span>
                <span>Delete</span>
              </button>
            {{/if}}
          </div>
        </div>
      </div>
      
      <hr>
      
      <div class="content">
        <div class="columns">
          <div class="column is-3 has-text-weight-semibold">File Name:</div>
          <div class="column">{{document.name}}</div>
        </div>
        
        <div class="columns">
          <div class="column is-3 has-text-weight-semibold">File Type:</div>
          <div class="column">
            <span class="tag is-light">{{document.mime_type}}</span>
          </div>
        </div>
        
        <div class="columns">
          <div class="column is-3 has-text-weight-semibold">File Size:</div>
          <div class="column">{{getFormattedSize document}}</div>
        </div>
        
        <div class="columns">
          <div class="column is-3 has-text-weight-semibold">Uploaded By:</div>
          <div class="column">
            {{#if (eq user.role 'admin')}}
              <a href="/admin/users/{{document.User.id}}">{{document.User.name}}</a>
            {{else}}
              {{document.User.name}}
            {{/if}}
          </div>
        </div>
        
        <div class="columns">
          <div class="column is-3 has-text-weight-semibold">Uploaded On:</div>
          <div class="column">{{formatDate document.created_at}}</div>
        </div>
        
        <div class="columns">
          <div class="column is-3 has-text-weight-semibold">Last Modified:</div>
          <div class="column">{{formatDate document.updated_at}}</div>
        </div>
        
        {{#if document.description}}
          <div class="columns">
            <div class="column is-3 has-text-weight-semibold">Description:</div>
            <div class="column">{{document.description}}</div>
          </div>
        {{/if}}
      </div>
      
      <hr>
      
      <div class="content">
        <h3 class="subtitle">Preview</h3>
        {{#if (eq (split document.mime_type '/')[0] 'image')}}
          <div class="has-text-centered">
            <img 
              src="/uploads/{{document.path}}" 
              alt="{{document.name}}" 
              style="max-width: 100%; max-height: 500px;"
              class="has-shadow"
            >
          </div>
        {{else if (eq document.mime_type 'application/pdf')}}
          <div class="has-text-centered">
            <iframe 
              src="/documents/{{document.id}}/view" 
              width="100%" 
              height="600"
              style="border: 1px solid #ddd;"
            ></iframe>
            <p class="help">
              Having trouble viewing the PDF? 
              <a href="/documents/{{document.id}}/download" class="has-text-link">Download it here</a>.
            </p>
          </div>
        {{else if (or (eq document.mime_type 'application/msword') (eq document.mime_type 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'))}}
          <div class="has-text-centered">
            <div class="box">
              <span class="icon is-large has-text-info">
                <i class="fas fa-file-word fa-5x"></i>
              </span>
              <p class="mt-3">
                <a href="/documents/{{document.id}}/download" class="button is-link">
                  <span class="icon">
                    <i class="fas fa-download"></i>
                  </span>
                  <span>Download Word Document</span>
                </a>
              </p>
            </div>
          </div>
        {{else}}
          <div class="box has-text-centered">
            <span class="icon is-large has-text-grey-light">
              <i class="fas fa-file fa-5x"></i>
            </span>
            <p class="mt-3">
              <a href="/documents/{{document.id}}/download" class="button is-link">
                <span class="icon">
                  <i class="fas fa-download"></i>
                </span>
                <span>Download File</span>
              </a>
            </p>
          </div>
        {{/if}}
      </div>
      
      <div class="content mt-6">
        <div class="level">
          <div class="level-left">
            <h3 class="subtitle">File Information</h3>
          </div>
          <div class="level-right">
            <div class="tags has-addons">
              <span class="tag is-dark">ID</span>
              <span class="tag is-light">{{document.id}}</span>
            </div>
          </div>
        </div>
        
        <div class="content">
          <pre><code>{{documentInfo}}</code></pre>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Delete Document Modal -->
<div class="modal" id="deleteDocumentModal">
  <div class="modal-background"></div>
  <div class="modal-card">
    <header class="modal-card-head">
      <p class="modal-card-title">Delete Document</p>
      <button class="delete" aria-label="close"></button>
    </header>
    <section class="modal-card-body">
      <p>Are you sure you want to delete <strong>{{document.name}}</strong>?</p>
      <p class="has-text-danger mt-3">This action cannot be undone.</p>
    </section>
    <footer class="modal-card-foot">
      <form id="deleteDocumentForm" method="POST" action="/documents/{{document.id}}/delete">
        <input type="hidden" name="_method" value="DELETE">
        <button type="button" class="button is-light cancel-delete">Cancel</button>
        <button type="submit" class="button is-danger">Delete</button>
      </form>
    </footer>
  </div>
</div>

{{#content "scripts"}}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Delete document modal
    const deleteButton = document.querySelector('.delete-document');
    const deleteModal = document.getElementById('deleteDocumentModal');
    const cancelDelete = document.querySelector('.cancel-delete');
    
    if (deleteButton) {
      deleteButton.addEventListener('click', function() {
        deleteModal.classList.add('is-active');
      });
      
      // Close modal when clicking the background or close button
      deleteModal.querySelector('.modal-background').addEventListener('click', () => {
        deleteModal.classList.remove('is-active');
      });
      
      deleteModal.querySelector('.delete').addEventListener('click', () => {
        deleteModal.classList.remove('is-active');
      });
      
      cancelDelete.addEventListener('click', () => {
        deleteModal.classList.remove('is-active');
      });
    }
  });
</script>
{{/content}}
