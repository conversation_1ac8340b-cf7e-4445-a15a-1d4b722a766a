{{!< ../layout}}

<section class="section">
  <div class="container">
    <div class="level">
      <div class="level-left">
        <h1 class="title">My Documents</h1>
      </div>
      <div class="level-right">
        <a href="/documents/upload" class="button is-primary">
          <span class="icon">
            <i class="fas fa-upload"></i>
          </span>
          <span>Upload Document</span>
        </a>
      </div>
    </div>

    <!-- Storage Quota -->
    <div class="box">
      <h2 class="subtitle">Storage Usage</h2>
      <div class="content">
        <div class="level is-mobile">
          <div class="level-left">
            <div class="level-item" style="gap: 0.25rem">
              <strong>{{usedMB}} MB</strong> of <strong>{{quotaMB}} MB</strong> used
            </div>
          </div>
          <div class="level-right">
            <div class="level-item">
              <strong>{{quotaPercentage}}%</strong> used
            </div>
          </div>
        </div>
        <progress
          class="progress {{#if (gte quotaPercentage 90)}}is-danger{{else if (gte quotaPercentage 75)}}is-warning{{else}}is-primary{{/if}}"
          value="{{quotaPercentage}}"
          max="100">
          {{quotaPercentage}}%
        </progress>
        <p class="help">{{remainingMB}} MB remaining</p>
      </div>
    </div>

    <!-- Documents List -->
    {{#if documents.length}}
      <div class="table-container">
        <table class="table is-fullwidth is-hoverable">
          <thead>
            <tr>
              <th>Name</th>
              <th>Author</th>
              <th>Type</th>
              <th>Size</th>
              <th>Uploaded</th>
              <th class="has-text-centered">Actions</th>
            </tr>
          </thead>
          <tbody>
            {{#each documents}}
              {{> document-table-row this}}
            {{/each}}
          </tbody>
        </table>
      </div>
    {{else}}
      <div class="notification is-light">
        <div class="content has-text-centered">
          <p class="mb-4">
            <span class="icon is-large">
              <i class="fas fa-file-upload fa-3x"></i>
            </span>
          </p>
          <p class="subtitle">No documents found</p>
          <p class="mb-4">Upload your first document to get started!</p>
          <a href="/documents/upload" class="button is-primary">
            <span class="icon">
              <i class="fas fa-upload"></i>
            </span>
            <span>Upload Document</span>
          </a>
        </div>
      </div>
    {{/if}}
  </div>
</section>

{{> edit-document-modal}}
{{> delete-document-modal}}

{{#section 'scripts'}}
<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Initialize all dropdowns
    const $dropdowns = document.querySelectorAll('.dropdown:not(.is-hoverable)');
    if ($dropdowns.length > 0) {
      $dropdowns.forEach($el => {
        $el.addEventListener('click', (event) => {
          event.stopPropagation();
          $el.classList.toggle('is-active');
        });
      });

      // Close dropdowns when clicking outside
      document.addEventListener('click', () => {
        $dropdowns.forEach($el => $el.classList.remove('is-active'));
      });
    }

    // Initialize mobile menu
    const $navbarBurgers = Array.prototype.slice.call(document.querySelectorAll('.navbar-burger'), 0);
    if ($navbarBurgers.length > 0) {
      $navbarBurgers.forEach(el => {
        el.addEventListener('click', () => {
          const target = el.dataset.target;
          const $target = document.getElementById(target);
          el.classList.toggle('is-active');
          $target.classList.toggle('is-active');
        });
      });
    }

    // Close notifications
    (document.querySelectorAll('.notification .delete') || []).forEach(($delete) => {
      $delete.addEventListener('click', () => {
        $delete.parentNode.remove();
      });
    });



    // Helper function to show notifications
    function showNotification(message, type = 'is-success') {
      const notification = document.createElement('div');
      notification.className = `notification ${type} is-light is-radiusless mb-0`;
      notification.innerHTML = `
        <button class="delete"></button>
        <p>${message}</p>
      `;

      // Insert after the navbar
      const navbar = document.querySelector('.navbar');
      navbar.parentNode.insertBefore(notification, navbar.nextSibling);

      // Auto-remove after 5 seconds
      setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
      }, 5000);

      // Close button
      notification.querySelector('.delete').addEventListener('click', () => {
        notification.remove();
      });
    }

    // Make showNotification available globally for other scripts
    window.showNotification = showNotification;
  });
</script>

{{> document-modals-script}}
{{/section}}
