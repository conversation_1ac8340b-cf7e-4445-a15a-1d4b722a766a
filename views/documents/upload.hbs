{{!< ../layout}}
{{#content 'head'}}
  <!-- Additional head content for upload page -->
  <style>
    .file-cta {
      justify-content: center;
    }
    .file-name {
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  </style>
{{/content}}

<div class="columns is-centered">
  <div class="column is-8-tablet is-6-desktop is-5-widescreen">
    <div class="box">
      <h1 class="title has-text-centered">Upload Document</h1>
      
      <form action="/documents" method="POST" enctype="multipart/form-data" id="uploadForm">
        <!-- File Upload -->
        <div class="field">
          <label class="label">Select File</label>
          <div class="file has-name is-fullwidth" id="fileUpload">
            <label class="file-label">
              <input class="file-input" type="file" name="document" id="document" required accept=".pdf">
              <span class="file-cta">
                <span class="file-icon">
                  <i class="fas fa-cloud-upload-alt"></i>
                </span>
                <span class="file-label">
                  Choose a file…
                </span>
              </span>
              <span class="file-name" id="fileName">
                No file selected
              </span>
            </label>
          </div>
          <p class="help">Maximum file size: 50MB</p>
        </div>

        <!-- Document Name -->
        <div class="field">
          <label class="label">Document Name</label>
          <div class="control has-icons-left">
            <input 
              class="input" 
              type="text" 
              name="name" 
              placeholder="Enter document name"
              id="documentName"
              value="{{formData.name}}"
            >
            <span class="icon is-small is-left">
              <i class="fas fa-file-alt"></i>
            </span>
          </div>
          <p class="help">Leave blank to use the original filename</p>
        </div>

        <!-- Authors -->
        <div class="field">
          <label class="label">Author(s)</label>
          <div class="control has-icons-left">
            <input 
              class="input" 
              type="text" 
              name="authors" 
              placeholder="Enter author name(s)" 
              value="{{formData.authors}}"
            >
            <span class="icon is-small is-left">
              <i class="fas fa-user-edit"></i>
            </span>
          </div>
        </div>

        <!-- Storage Info -->
        {{#if user}}
          <div class="notification is-light">
            <div class="level is-mobile">
              <div class="level-left">
                <div class="level-item" style="gap: 0.25rem">
                  <span class="icon has-text-info">
                    <i class="fas fa-hdd"></i>
                  </span>
                </div>
                <div class="level-item">
                  <div>
                    <p class="heading">Storage Used</p>
                    <p class="title is-6">{{usedMB}} MB <span class="has-text-grey">/ {{quotaMB}} MB</span></p>
                  </div>
                </div>
              </div>
              <div class="level-right ">
                <div>
                  <progress 
                    class="progress is-small {{#if (gte quotaPercentage 90)}}is-danger{{else if (gte quotaPercentage 70)}}is-warning{{else}}is-primary{{/if}}" 
                    value="{{quotaPercentage}}" 
                    max="100">
                    {{quotaPercentage}}%
                  </progress>
                  <p class="has-text-centered">{{remainingMB}} MB remaining</p>
                </div>
              </div>
            </div>
          </div>
        {{/if}}

        <!-- Submit Button -->
        <div class="field">
          <div class="control">
            <button type="submit" class="button is-primary is-fullwidth" id="submitBtn">
              <span class="icon">
                <i class="fas fa-upload"></i>
              </span>
              <span>Upload Document</span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Upload Progress Modal -->
<div class="modal" id="uploadProgressModal">
  <div class="modal-background"></div>
  <div class="modal-card">
    <header class="modal-card-head">
      <p class="modal-card-title">Uploading File</p>
    </header>
    <section class="modal-card-body has-text-centered">
      <div class="mb-4">
        <span class="icon is-large has-text-primary">
          <i class="fas fa-cloud-upload-alt fa-3x"></i>
        </span>
      </div>
      <p id="uploadingFileName" class="has-text-weight-bold mb-4"></p>
      <progress class="progress is-primary" id="uploadProgress" value="0" max="100">0%</progress>
      <p class="has-text-centered" id="uploadStatus">Preparing upload...</p>
    </section>
  </div>
</div>

{{#content "scripts"}}
<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Update file name display
    const fileInput = document.getElementById('document');
    const fileName = document.getElementById('fileName');
    const documentName = document.getElementById('documentName');
    
    fileInput.addEventListener('change', (e) => {
      if (fileInput.files.length > 0) {
        const file = fileInput.files[0];
        fileName.textContent = file.name;
        
        // Set document name to filename without extension if empty
        if (!documentName.value) {
          const name = file.name.replace(/\.[^/.]+$/, ''); // Remove extension
          documentName.value = name;
        }
      } else {
        fileName.textContent = 'No file selected';
      }
    });
    
    // Handle form submission
    const uploadForm = document.getElementById('uploadForm');
    const uploadProgressModalEl = document.getElementById('uploadProgressModal'); // Get modal element
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadStatus = document.getElementById('uploadStatus');
    const uploadingFileName = document.getElementById('uploadingFileName');
    
    uploadForm.addEventListener('submit', (e) => {
      e.preventDefault();
      
      const formData = new FormData(uploadForm);
      const xhr = new XMLHttpRequest();
      
      // Show upload modal
      const file = document.getElementById('document').files[0];
      uploadingFileName.textContent = file ? file.name : 'Unknown file';
      uploadProgressModalEl.classList.add('is-active'); // Show Bulma modal
      
      // Upload progress
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const percentComplete = Math.round((e.loaded / e.total) * 100);
          uploadProgress.value = percentComplete;
          uploadProgress.textContent = `${percentComplete}%`;
          uploadStatus.textContent = `Uploading... ${percentComplete}%`;
        }
      });
      
      // Upload complete
      xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
          uploadStatus.textContent = 'Upload complete! Redirecting...';
          setTimeout(() => {
            uploadProgressModalEl.classList.remove('is-active'); // Hide Bulma modal
            window.location.href = '/documents';
          }, 1500); // Increased delay for redirect message
        } else {
          uploadStatus.innerHTML = `<span class="has-text-danger">Upload failed: ${xhr.responseText || 'Unknown error'}</span>`;
          setTimeout(() => {
            uploadProgressModalEl.classList.remove('is-active'); // Hide Bulma modal
          }, 3000); // Keep modal a bit longer for error
        }
      });
      
      // Upload failed
      xhr.addEventListener('error', () => {
        uploadStatus.innerHTML = '<span class="has-text-danger">Upload failed. Please try again.</span>';
        setTimeout(() => {
          uploadProgressModalEl.classList.remove('is-active'); // Hide Bulma modal
        }, 3000);
      });
      
      // Start upload
      xhr.open('POST', uploadForm.action, true);
      xhr.send(formData);
    });
  });
</script>
{{/content}}
