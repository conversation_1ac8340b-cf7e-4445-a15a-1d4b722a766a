<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{title}} - Document Management System</title>
  
  <!-- Favicon -->
  <link rel="icon" href="https://bulma.io/favicons/favicon.ico" type="image/x-icon">
  
  <!-- Bulma CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
  
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- Custom CSS -->
  <link rel="stylesheet" href="/stylesheets/style.css">
  
  <!-- CSRF Token for AJAX requests -->
  <meta name="csrf-token" content="{{csrfToken}}">
  
  <!-- Additional head content -->
  {{{_sections.head}}}
</head>
<body>
  <div id="app" class="has-navbar-fixed-top">
    <!-- Navigation -->
    {{> navigation }}
    
    <!-- Flash Messages -->
    {{#if success_msg}}
      <div class="notification is-success is-light is-radiusless mb-0">
        <button class="delete"></button>
        <p>{{success_msg}}</p>
      </div>
    {{/if}}
    
    {{#if error_msg}}
      <div class="notification is-danger is-light is-radiusless mb-0">
        <button class="delete"></button>
        <p>{{error_msg}}</p>
      </div>
    {{/if}}
    
    {{#if error}}
      <div class="notification is-danger is-light is-radiusless mb-0">
        <button class="delete"></button>
        <p>{{error}}</p>
      </div>
    {{/if}}
    
    <!-- Main Content -->
    <main class="main-content">
      {{{body}}}
    </main>
    
    <!-- Footer -->
    <footer class="footer">
      <div class="content has-text-centered">
        <p>
          <strong>Document Management System</strong> by DocManager. The source code is licensed 
          <a href="http://opensource.org/licenses/mit-license.php">MIT</a>.
        </p>
      </div>
    </footer>
    
    <!-- Modals will be loaded here -->
    <div id="modal-container"></div>
  </div>
  
  <!-- Custom JS -->
  <script src="/javascripts/main.js" defer></script>
  
  <!-- Page-specific scripts -->
  {{{_sections.scripts}}}
  
  <!-- Initialize JavaScript -->

  </script>
</body>
</html>
