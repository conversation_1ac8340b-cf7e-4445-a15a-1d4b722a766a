{{!--
  User Modals JavaScript Partial

  Shared JavaScript functionality for user delete and status toggle modals
--}}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Delete user modal
    const deleteButtons = document.querySelectorAll('.delete-user');
    const deleteModal = document.getElementById('deleteUserModal');
    const deleteForm = document.getElementById('deleteUserForm');
    const deleteUserName = document.getElementById('deleteUserName');
    const cancelDelete = document.querySelector('.cancel-delete');

    deleteButtons.forEach(button => {
      button.addEventListener('click', function() {
        const userId = this.getAttribute('data-id');
        const userName = this.getAttribute('data-name') || 'this user';
        deleteUserName.textContent = userName;
        deleteForm.action = `/admin/users/${userId}`;
        deleteModal.classList.add('is-active');
      });
    });

    // Close delete modal
    deleteModal.querySelector('.modal-background').addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });

    deleteModal.querySelector('.delete').addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });

    cancelDelete.addEventListener('click', () => {
      deleteModal.classList.remove('is-active');
    });


    // Close notifications when clicking the delete button
    document.querySelectorAll('.notification .delete').forEach(deleteButton => {
      deleteButton.addEventListener('click', function() {
        this.closest('.notification').remove();
      });
    });
  });
</script>
