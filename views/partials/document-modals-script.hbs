{{!--
  Document Modals JavaScript Partial

  Shared JavaScript functionality for edit and delete modals
  Works with both user and admin contexts
--}}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Edit document functionality
    const editModal = document.getElementById('editModal');
    const editForm = document.getElementById('editDocumentForm');
    const editNameInput = document.getElementById('editName');
    const editAuthorsInput = document.getElementById('editAuthors');
    let documentIdToEdit = null;

    // Set up edit buttons
    document.querySelectorAll('.edit-document').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        documentIdToEdit = button.dataset.id;
        editNameInput.value = button.dataset.name;
        editAuthorsInput.value = button.dataset.authors || '';
        editModal.classList.add('is-active');
      });
    });

    // Confirm edit
    document.getElementById('confirmEdit').addEventListener('click', async () => {
      if (!documentIdToEdit) return;

      try {
        const response = await fetch(`/documents/${documentIdToEdit}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          },
          body: JSON.stringify({
            name: editNameInput.value,
            authors: editAuthorsInput.value
          })
        });

        if (response.ok) {
          window.location.reload();
        } else {
          const error = await response.json();
          const message = error.message || 'Failed to update document';
          if (window.showNotification) {
            window.showNotification(message, 'is-danger');
          } else {
            alert(message);
          }
        }
      } catch (error) {
        console.error('Error updating document:', error);
        const message = 'An error occurred while updating the document';
        if (window.showNotification) {
          window.showNotification(message, 'is-danger');
        } else {
          alert(message);
        }
      } finally {
        editModal.classList.remove('is-active');
      }
    });

    // Close edit modal
    document.querySelectorAll('#editModal .modal-background, #editModal .delete, #cancelEdit').forEach(el => {
      el.addEventListener('click', () => {
        editModal.classList.remove('is-active');
      });
    });

    // Delete document functionality
    const deleteModal = document.getElementById('deleteModal');
    const deleteDocumentName = document.getElementById('deleteDocumentName');
    let documentIdToDelete = null;

    // Set up delete buttons
    document.querySelectorAll('.delete-document').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        documentIdToDelete = button.dataset.id;
        deleteDocumentName.textContent = button.dataset.name || 'this document';
        deleteModal.classList.add('is-active');
      });
    });

    // Confirm delete
    document.getElementById('confirmDelete').addEventListener('click', async () => {
      if (!documentIdToDelete) return;

      try {
        const response = await fetch(`/documents/${documentIdToDelete}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
          }
        });

        if (response.ok) {
          window.location.reload();
        } else {
          const error = await response.json();
          const message = error.message || 'Failed to delete document';
          if (window.showNotification) {
            window.showNotification(message, 'is-danger');
          } else {
            alert(message);
          }
        }
      } catch (error) {
        console.error('Error deleting document:', error);
        const message = 'An error occurred while deleting the document';
        if (window.showNotification) {
          window.showNotification(message, 'is-danger');
        } else {
          alert(message);
        }
      } finally {
        deleteModal.classList.remove('is-active');
      }
    });

    // Close delete modal
    document.querySelectorAll('#deleteModal .modal-background, #deleteModal .delete, #cancelDelete').forEach(el => {
      el.addEventListener('click', () => {
        deleteModal.classList.remove('is-active');
      });
    });
  });
</script>
