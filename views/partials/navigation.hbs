<nav class="navbar is-primary" role="navigation" aria-label="main navigation">
  <div class="container">
    <div class="navbar-brand">
      <a class="navbar-item" href="/">
        <i class="fas fa-book-open mr-2"></i>
        <span class="has-text-weight-bold">DocManager</span>
      </a>

      <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbarBasic">
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
        <span aria-hidden="true"></span>
      </a>
    </div>

    <div id="navbarBasic" class="navbar-menu">
      <div class="navbar-start">
        {{#if user}}
          <a class="navbar-item" href="/documents">
            <span class="icon">
              <i class="fas fa-home"></i>
            </span>
            <span>Home</span>
          </a>
          
          <a class="navbar-item" href="/documents/upload">
            <span class="icon">
              <i class="fas fa-upload"></i>
            </span>
            <span>Upload</span>
          </a>
        {{/if}}
      </div>

      <div class="navbar-end">
        {{#if user}}
          {{#if user.is_admin}}
            <div class="navbar-item has-dropdown is-hoverable">
              <a class="navbar-link">
                <span class="icon">
                  <i class="fas fa-shield-alt"></i>
                </span>
                <span>Admin</span>
              </a>

              <div class="navbar-dropdown">
                <a class="navbar-item" href="/admin/dashboard">
                  <span class="icon">
                    <i class="fas fa-tachometer-alt"></i>
                  </span>
                  <span>Dashboard</span>
                </a>
                <a class="navbar-item" href="/admin/users">
                  <span class="icon">
                    <i class="fas fa-users"></i>
                  </span>
                  <span>Manage Users</span>
                </a>
                <a class="navbar-item" href="/admin/documents">
                  <span class="icon">
                    <i class="fas fa-file-alt"></i>
                  </span>
                  <span>Manage Documents</span>
                </a>
              </div>
            </div>
          {{/if}}
          
          <div class="navbar-item has-dropdown is-hoverable">
            <a class="navbar-link">
              <span class="icon">
                <i class="fas fa-user"></i>
              </span>
              <span>{{user.email}}</span>
            </a>
            <div class="navbar-dropdown is-right">
              <a class="navbar-item" href="/auth/logout">
                <span class="icon">
                  <i class="fas fa-sign-out-alt"></i>
                </span>
                <span>Logout</span>
              </a>
            </div>
          </div>
        {{else}}
          <div class="navbar-item">
            <div class="buttons">
              <a class="button is-light" href="/auth/login">
                <span class="icon">
                  <i class="fas fa-sign-in-alt"></i>
                </span>
                <span>Log in</span>
              </a>
            </div>
          </div>
        {{/if}}
      </div>
    </div>
  </div>
</nav>

<!-- Mobile menu toggle -->
<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Get all "navbar-burger" elements
    const $navbarBurgers = Array.prototype.slice.call(document.querySelectorAll('.navbar-burger'), 0);

    // Add a click event on each of them
    $navbarBurgers.forEach(el => {
      el.addEventListener('click', () => {
        // Get the target from the "data-target" attribute
        const target = el.dataset.target;
        const $target = document.getElementById(target);

        // Toggle the "is-active" class on both the "navbar-burger" and the "navbar-menu"
        el.classList.toggle('is-active');
        $target.classList.toggle('is-active');
      });
    });
  });
</script>
