const bcrypt = require('bcryptjs');

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true,
      },
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    is_admin: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    quota_mb: {
      type: DataTypes.INTEGER,
      defaultValue: 100,
      validate: {
        min: 0,
      },
    },
  }, {
    tableName: 'users',
    timestamps: true,
    underscored: true,
  });

  // Instance method to check password
  User.prototype.validPassword = function(password) {
    return bcrypt.compareSync(password, this.password);
  };

  // Hook to hash password before creating/updating
  User.beforeSave(async (user) => {
    if (user.changed('password')) {
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(user.password, salt);
    }
  });

  // Instance method to get used storage in MB
  User.prototype.getUsedStorageMB = async function() {
    const documents = await this.getDocuments();
    const totalBytes = documents.reduce((sum, doc) => sum + doc.size_bytes, 0);
    return Math.ceil(totalBytes / (1024 * 1024));
  };

  // Instance method to check if user has enough quota for a file of given size
  User.prototype.hasEnoughQuota = async function(fileSizeBytes) {
    const usedMB = await this.getUsedStorageMB();
    const fileSizeMB = Math.ceil(fileSizeBytes / (1024 * 1024));
    return (usedMB + fileSizeMB) <= this.quota_mb;
  };

  // Define associations
  User.associate = function(models) {
    User.hasMany(models.Document, {
      foreignKey: 'user_id',
      as: 'documents'
    });
  };

  return User;
};
