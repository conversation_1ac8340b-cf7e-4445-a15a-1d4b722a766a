#!/usr/bin/env python3
"""
Code to PDF Generator
Crawls folders for JS/JSON files and creates a PDF with project structure and syntax-highlighted code.
"""

import os
import argparse
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, Preformatted
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import black, blue, green
from pygments.lexers import JavascriptLexer, JsonLexer, get_lexer_by_name
from pygments.util import ClassNotFound


class CodeToPDFGenerator:
    def __init__(self, output_file="code_documentation.pdf"):
        self.output_file = output_file
        self.doc = SimpleDocTemplate(output_file, pagesize=A4)
        self.styles = getSampleStyleSheet()
        self.story = []

        # Custom styles
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=blue
        )

        self.heading_style = ParagraphStyle(
            'CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            spaceBefore=20,
            textColor=black
        )

        self.file_path_style = ParagraphStyle(
            'FilePath',
            parent=self.styles['Normal'],
            fontSize=12,
            textColor=blue,
            fontName='Courier-Bold'
        )

        self.code_style = ParagraphStyle(
            'Code',
            parent=self.styles['Code'],
            fontSize=8,
            fontName='Courier',
            leftIndent=20,
            rightIndent=20
        )

        self.stats_style = ParagraphStyle(
            'Stats',
            parent=self.styles['Normal'],
            fontSize=10,
            textColor=green,
            fontName='Courier'
        )

    def get_project_structure(self, root_path, extensions=('.js', '.json')):
        """Generate a tree-like project structure."""
        structure = []

        def add_directory(path, prefix="", is_last=True):
            items = []
            try:
                for item in sorted(os.listdir(path)):
                    item_path = os.path.join(path, item)
                    if os.path.isfile(item_path) and item.endswith(extensions):
                        items.append((item, item_path, False))
                    elif os.path.isdir(item_path) and not item.startswith('.'):
                        items.append((item, item_path, True))
            except PermissionError:
                return

            for i, (name, full_path, is_dir) in enumerate(items):
                is_last_item = i == len(items) - 1
                connector = "└── " if is_last_item else "├── "
                structure.append(f"{prefix}{connector}{name}")

                if is_dir:
                    extension = "    " if is_last_item else "│   "
                    add_directory(full_path, prefix + extension, is_last_item)

        structure.append(os.path.basename(root_path) + "/")
        add_directory(root_path)
        return "\n".join(structure)

    def get_file_stats(self, file_path):
        """Get file statistics."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                return {
                    'total_lines': len(lines),
                    'non_empty_lines': len([line for line in lines if line.strip()]),
                    'size_bytes': len(content.encode('utf-8'))
                }
        except Exception as e:
            return {'error': str(e)}

    def format_file_size(self, size_bytes):
        """Format file size in human readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"

    def add_title_page(self, root_path):
        """Add title page to the PDF."""
        self.story.append(Paragraph("Code Documentation", self.title_style))
        self.story.append(Spacer(1, 0.5*inch))
        self.story.append(Paragraph(f"Project: {os.path.basename(root_path)}", self.heading_style))
        self.story.append(Paragraph(f"Path: {root_path}", self.file_path_style))
        self.story.append(Spacer(1, 0.3*inch))
        self.story.append(PageBreak())

    def add_project_structure(self, directories):
        """Add project structure section for all directories."""
        self.story.append(Paragraph("Project Structure", self.heading_style))
        self.story.append(Spacer(1, 0.2*inch))

        all_structures = []
        for directory in directories:
            if len(directories) > 1:
                all_structures.append(f"\n{os.path.basename(directory)}/")
            structure = self.get_project_structure(directory)
            all_structures.append(structure)

        combined_structure = "\n\n".join(all_structures)
        self.story.append(Preformatted(combined_structure, self.code_style))
        self.story.append(PageBreak())

    def get_lexer_for_file(self, file_path):
        """Get appropriate Pygments lexer for file."""
        extension = os.path.splitext(file_path)[1].lower()
        try:
            if extension == '.js':
                return JavascriptLexer()
            elif extension == '.json':
                return JsonLexer()
            else:
                return get_lexer_by_name('text')
        except ClassNotFound:
            return get_lexer_by_name('text')

    def add_file_content(self, file_path, relative_path):
        """Add file content with syntax highlighting."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # File header
            self.story.append(Paragraph(f"File: {relative_path}", self.file_path_style))

            # File stats
            stats = self.get_file_stats(file_path)
            if 'error' not in stats:
                stats_text = (f"Lines: {stats['total_lines']} | "
                            f"Non-empty: {stats['non_empty_lines']} | "
                            f"Size: {self.format_file_size(stats['size_bytes'])}")
                self.story.append(Paragraph(stats_text, self.stats_style))

            self.story.append(Spacer(1, 0.1*inch))

            # Code content (without syntax highlighting for PDF compatibility)
            # Split long lines and add line numbers
            lines = content.split('\n')
            formatted_lines = []
            for i, line in enumerate(lines, 1):
                # Truncate very long lines
                if len(line) > 100:
                    line = line[:97] + "..."
                formatted_lines.append(f"{i:4d}: {line}")

            formatted_content = '\n'.join(formatted_lines)
            self.story.append(Preformatted(formatted_content, self.code_style))
            self.story.append(PageBreak())

        except Exception as e:
            error_text = f"Error reading file {relative_path}: {str(e)}"
            self.story.append(Paragraph(error_text, self.styles['Normal']))
            self.story.append(PageBreak())

    def crawl_and_generate(self, root_paths, extensions=('.js', '.json')):
        """Main method to crawl directories and generate PDF."""
        if isinstance(root_paths, str):
            root_paths = [root_paths]

        all_files = []
        directories = []

        # Collect all files
        for path in root_paths:
            path = os.path.abspath(path)
            if not os.path.exists(path):
                print(f"Warning: Path {path} does not exist")
                continue

            if os.path.isfile(path):
                # Handle individual files
                if path.endswith(extensions):
                    relative_path = os.path.basename(path)
                    all_files.append((path, relative_path, os.path.dirname(path)))
            else:
                # Handle directories
                directories.append(path)
                for root, dirs, files in os.walk(path):
                    # Skip hidden directories and common build/dependency directories
                    dirs[:] = [d for d in dirs if not d.startswith('.') and
                              d not in ['node_modules', 'dist', 'build', '__pycache__']]

                    for file in files:
                        if file.endswith(extensions):
                            file_path = os.path.join(root, file)
                            relative_path = os.path.relpath(file_path, path)
                            all_files.append((file_path, relative_path, path))

        if not all_files:
            print("No JS or JSON files found in the specified directories.")
            return

        # Add title page
        title = root_paths[0] if len(root_paths) == 1 else "Multiple Projects"
        self.add_title_page(title)

        # Add project structure for all directories on one page
        if directories:
            self.add_project_structure(directories)

        # Add file contents
        self.story.append(Paragraph("File Contents", self.heading_style))
        self.story.append(Spacer(1, 0.2*inch))

        for file_path, relative_path, root_path in sorted(all_files):
            self.add_file_content(file_path, relative_path)

        # Generate PDF
        print(f"Generating PDF: {self.output_file}")
        self.doc.build(self.story)
        print(f"PDF generated successfully: {self.output_file}")


def main():
    parser = argparse.ArgumentParser(description='Generate PDF documentation from JS/JSON files')
    parser.add_argument('paths', nargs='+', help='Directories to crawl for JS/JSON files')
    parser.add_argument('-o', '--output', default='code_documentation.pdf',
                       help='Output PDF filename (default: code_documentation.pdf)')
    parser.add_argument('-e', '--extensions', nargs='+', default=['.js', '.json'],
                       help='File extensions to include (default: .js .json)')

    args = parser.parse_args()

    # Ensure extensions start with dot
    extensions = tuple(ext if ext.startswith('.') else f'.{ext}' for ext in args.extensions)

    generator = CodeToPDFGenerator(args.output)
    generator.crawl_and_generate(args.paths, extensions)


if __name__ == "__main__":
    main()
