const { Sequelize, DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const config = require('../config/database');
const path = require('path');
const fs = require('fs');

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

const sequelize = new Sequelize({
  ...dbConfig,
  define: {
    timestamps: true,
    underscored: true,
  },
});

// Import models
const User = require('../models/user')(sequelize, DataTypes);
const Document = require('../models/document')(sequelize, DataTypes);

// Define associations
User.hasMany(Document, { foreignKey: 'user_id' });
Document.belongsTo(User, { foreignKey: 'user_id' });

// Sync database and create default users
const initDb = async () => {
  try {
    // Sync all models
    await sequelize.sync({ force: true });
    console.log('Database synchronized');

    // Create default admin user
    const adminUser = await User.create({
      email: '<EMAIL>',
      password: 'admin',
      is_admin: true,
      quota_mb: 1000, // 1GB for admin
    });

    // Create default regular user
    const regularUser = await User.create({
      email: '<EMAIL>',
      password: 'user',
      is_admin: false,
      quota_mb: 100, // 100MB for regular user
    });

    console.log('Default users created successfully');
    console.log('Admin: <EMAIL> / admin');
    console.log('User: <EMAIL> / user');
    
    process.exit(0);
  } catch (error) {
    console.error('Error initializing database:', error);
    process.exit(1);
  }
};

initDb();
