const passport = require('passport');
const LocalStrategy = require('passport-local').Strategy;
const { User } = require('../models');

// Configure the local strategy for use by Passport
passport.use(new LocalStrategy({
  usernameField: 'email',
  passwordField: 'password',
}, async (email, password, done) => {
  try {
    // Find the user by email
    const user = await User.findOne({ where: { email } });
    // If user not found or password is incorrect
    if (!user || !user.validPassword(password)) {
      return done(null, false, { message: 'Incorrect email or password' });
    }
    
    // Authentication successful
    return done(null, user);
  } catch (error) {
    return done(error);
  }
}));

// Configure Passport authenticated session persistence
passport.serializeUser((user, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
  try {
    const user = await User.findByPk(id);
    done(null, user);
  } catch (error) {
    done(error);
  }
});

// Middleware to check if user is authenticated
const isAuthenticated = (req, res, next) => {
  if (req.isAuthenticated()) {
    return next();
  }
  req.flash('error', 'Please log in to access this page');
  res.redirect('/auth/login');
};

// Middleware to check if user is an admin
const isAdmin = (req, res, next) => {
  if (req.isAuthenticated() && req.user.is_admin) {
    return next();
  }
  req.flash('error', 'You do not have permission to access this page');
  res.redirect('/');
};

// Middleware to check if user is the owner of the resource
const isOwner = async (req, res, next) => {
  try {
    const documentId = req.params.id;
    const { Document } = require('../models');
    
    // Check if user is admin (admins can access any document)
    if (req.user.is_admin) {
      return next();
    }
    
    // Check if the document belongs to the user
    const document = await Document.findOne({
      where: { 
        id: documentId,
        user_id: req.user.id
      }
    });
    
    if (document) {
      return next();
    }
    
    req.flash('error', 'You do not have permission to access this resource');
    res.redirect('/');
  } catch (error) {
    next(error);
  }
};

module.exports = {
  passport,
  isAuthenticated,
  isAdmin,
  isOwner,
};
