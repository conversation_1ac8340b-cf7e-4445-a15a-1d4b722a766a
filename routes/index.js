const express = require('express');
const router = express.Router();
const { isAuthenticated } = require('../middlewares/auth');

// GET / - Home page
router.get('/', (req, res) => {
  if (req.isAuthenticated()) {
    return res.redirect('/documents');
  }
  res.render('index', { 
    title: 'Welcome to Document Management System',
    isHome: true
  });
});

// GET /dashboard - User dashboard (redirects to documents)
router.get('/dashboard', isAuthenticated, (req, res) => {
  res.redirect('/documents');
});

module.exports = router;
