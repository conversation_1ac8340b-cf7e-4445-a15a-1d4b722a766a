const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs').promises;
const { check, validationResult } = require('express-validator');
const { isAuthenticated, isOwner } = require('../middlewares/auth');
const { handleFileUpload } = require('../middlewares/upload');
const { Document, User } = require('../models');

// Middleware to check if user can upload files
const canUpload = async (req, res, next) => {
  try {
    const file = req.file;
    if (!file) {
      req.flash('error_msg', 'Please select a file to upload');
      return res.redirect('/documents/upload');
    }

    // Check if the file is a PDF
    if (file.mimetype !== 'application/pdf') {
      await fs.unlink(file.path).catch(console.error); // Delete non-PDF file
      req.flash('error_msg', 'Only PDF files are allowed.');
      return res.redirect('/documents/upload');
    }

    const hasQuota = await req.user.hasEnoughQuota(file.size);
    if (!hasQuota) {
      // Delete the uploaded file if quota is exceeded
      await fs.unlink(file.path).catch(console.error);
      req.flash('error_msg', 'Not enough storage quota to upload this file');
      return res.redirect('/documents/upload');
    }

    next();
  } catch (error) {
    next(error);
  }
};

// GET /documents - List all documents for the current user
router.get('/', isAuthenticated, async (req, res, next) => {
  try {
    const documents = await req.user.getDocuments({
      order: [['created_at', 'DESC']]
    });
    
    const usedMB = await req.user.getUsedStorageMB();
    const quotaMB = req.user.quota_mb;
    const quotaPercentage = Math.min(Math.round((usedMB / quotaMB) * 100), 100);
    
    res.render('documents/index', {
      title: 'My Documents',
      documents,
      usedMB,
      quotaMB,
      quotaPercentage,
      remainingMB: quotaMB - usedMB
    });
  } catch (error) {
    next(error);
  }
});

// GET /documents/upload - Show upload form
router.get('/upload', isAuthenticated, async (req, res, next) => {
  try {
    const usedMB = await req.user.getUsedStorageMB();
    const quotaMB = req.user.quota_mb;
    const quotaPercentage = Math.min(Math.round((usedMB / quotaMB) * 100), 100);
    
    res.render('documents/upload', { 
      title: 'Upload Document',
      usedMB,
      quotaMB,
      quotaPercentage,
      remainingMB: quotaMB - usedMB
    });
  } catch (error) {
    next(error);
  }
});

// POST /documents - Handle file upload
router.post('/', 
  isAuthenticated,
  handleFileUpload('document'),
  canUpload,
  async (req, res, next) => {
    try {
      const { name = '', authors = '' } = req.body;
      const file = req.file;

      // Create document in database
      await Document.create({
        name: name || path.parse(file.originalname).name,
        authors: authors || 'Unknown',
        file_name: file.originalname,
        file_path: file.path,
        mime_type: file.mimetype,
        size_bytes: file.size,
        user_id: req.user.id
      });

      req.flash('success_msg', 'Document uploaded successfully');
      res.redirect('/documents');
    } catch (error) {
      // Clean up the uploaded file if there was an error
      if (req.file) {
        await fs.unlink(req.file.path).catch(console.error);
      }
      next(error);
    }
  }
);

// GET /documents/:id - View document details
router.get('/:id', isAuthenticated, isOwner, async (req, res, next) => {
  try {
    const document = await Document.findByPk(req.params.id);
    if (!document) {
      req.flash('error_msg', 'Document not found');
      return res.redirect('/documents');
    }

    // Render the document details view
    res.render('documents/view', {
      title: document.name,
      document: {
        id: document.id,
        name: document.name,
        authors: document.authors,
        file_name: document.file_name,
        mime_type: document.mime_type,
        size_bytes: document.size_bytes,
        formatted_size: document.getFormattedSize(),
        created_at: document.created_at,
        download_url: `/documents/${document.id}/download`,
        view_url: `/documents/${document.id}/view`
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /documents/:id/download - Download document
router.get('/:id/download', isAuthenticated, isOwner, async (req, res, next) => {
  try {
    const document = await Document.findByPk(req.params.id);
    if (!document) {
      req.flash('error_msg', 'Document not found');
      return res.redirect('/documents');
    }

    res.download(document.file_path, document.file_name);
  } catch (error) {
    next(error);
  }
});

// GET /documents/:id/view - View document in browser
router.get('/:id/view', isAuthenticated, isOwner, async (req, res, next) => {
  try {
    const document = await Document.findByPk(req.params.id);
    if (!document) {
      req.flash('error_msg', 'Document not found');
      return res.redirect('/documents');
    }

    // For PDFs, images, and text files, we can display them directly
    if (
      document.mime_type === 'application/pdf' ||
      document.mime_type.startsWith('image/') ||
      document.mime_type.startsWith('text/')
    ) {
      return res.sendFile(document.file_path);
    }

    // For other file types, force download
    res.download(document.file_path, document.file_name);
  } catch (error) {
    next(error);
  }
});

// PUT /documents/:id - Update document metadata
router.put('/:id', 
  isAuthenticated, 
  isOwner,
  [
    check('name').trim().notEmpty().withMessage('Name is required'),
    check('authors').trim().notEmpty().withMessage('Authors field is required')
  ],
  async (req, res, next) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          errors: errors.array()
        });
      }

      const { name, authors } = req.body;
      const document = await Document.findByPk(req.params.id);
      
      if (!document) {
        return res.status(404).json({
          success: false,
          message: 'Document not found'
        });
      }

      await document.update({ name, authors });
      
      res.json({
        success: true,
        message: 'Document updated successfully',
        document: {
          id: document.id,
          name: document.name,
          authors: document.authors
        }
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /documents/:id - Delete document
router.delete('/:id', isAuthenticated, isOwner, async (req, res, next) => {
  try {
    const document = await Document.findByPk(req.params.id);
    if (!document) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }

    // Delete the file from the filesystem
    await fs.unlink(document.file_path).catch(console.error);
    
    // Delete the record from the database
    await document.destroy();
    
    res.json({
      success: true,
      message: 'Document deleted successfully'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
