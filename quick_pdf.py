#!/usr/bin/env python3
"""
Quick PDF Generator - Simple version for common use cases
"""

from code_to_pdf import CodeToPDFGenerator
import sys

def main():
    if len(sys.argv) < 2:
        print("Usage: python quick_pdf.py <directory_or_file> [output_name.pdf]")
        print("Examples:")
        print("  python quick_pdf.py .")
        print("  python quick_pdf.py ./routes my_routes.pdf")
        print("  python quick_pdf.py package.json")
        return
    
    path = sys.argv[1]
    output_name = sys.argv[2] if len(sys.argv) > 2 else "code_documentation.pdf"
    
    generator = CodeToPDFGenerator(output_name)
    generator.crawl_and_generate(path)

if __name__ == "__main__":
    main()
